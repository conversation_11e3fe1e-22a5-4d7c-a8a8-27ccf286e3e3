import React from 'react';

const FeaturesSection = () => {
  const features = [
    {
      title: 'Consulenza Personalizzata',
      description: 'I nostri esperti ti aiutano a scegliere la montatura perfetta per il tuo viso e le tue esigenze.',
      icon: '👥'
    },
    {
      title: 'Lenti di Qualità',
      description: 'Utilizziamo solo lenti di alta qualità con trattamenti antiriflesso e protezione UV.',
      icon: '🔍'
    },
    {
      title: 'Montature Esclusive',
      description: 'Ampia selezione di montature dei migliori brand internazionali e designer.',
      icon: '✨'
    },
    {
      title: 'Controllo Vista',
      description: 'Esa<PERSON> della vista accurati con strumentazione professionale di ultima generazione.',
      icon: '👁️'
    },
    {
      title: 'Assistenza Post-Vendita',
      description: 'Servizio di manutenzione, riparazioni e aggiustamenti per i tuoi occhiali.',
      icon: '🔧'
    },
    {
      title: 'Garanzia Qualità',
      description: 'Tutti i nostri prodotti sono coperti da garanzia per la massima tranquillità.',
      icon: '🛡️'
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
            Perché Scegliere Ottica GR1
          </h2>
          <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
            Da oltre 40 anni offriamo servizi di eccellenza per la cura della tua vista
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 bg-background rounded-lg hover:shadow-lg transition-shadow duration-300">
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold font-sans text-text-base mb-3">
                {feature.title}
              </h3>
              <p className="text-text-base opacity-80 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
