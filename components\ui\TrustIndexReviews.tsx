'use client';

import { useEffect } from 'react';

interface TrustIndexReviewsProps {
  className?: string;
}

export default function TrustIndexReviews({ className = '' }: TrustIndexReviewsProps) {
  useEffect(() => {
    // Ensure TrustIndex script is loaded and initialized
    if (typeof window !== 'undefined' && (window as any).trustindex) {
      (window as any).trustindex.init();
    }
  }, []);

  return (
    <section className={`py-12 bg-gray-50 ${className}`}>
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Cosa dicono i nostri clienti
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Scopri le recensioni dei nostri clienti soddisfatti e la loro esperienza con Ottica GR1
          </p>
        </div>
        
        {/* TrustIndex Reviews Widget */}
        <div className="flex justify-center">
          <div
            className="w-full max-w-4xl"
            dangerouslySetInnerHTML={{
              __html: `<div src='https://cdn.trustindex.io/loader.js?a5b0a554919a854f166675fc15f'></div>`
            }}
          />
        </div>
      </div>
    </section>
  );
}
