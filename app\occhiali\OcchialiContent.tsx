'use client';

import Header from '../../components/layout/Header';
import Footer from '../../components/layout/Footer';
import Carousel from '../../components/ui/Carousel';
import Button from '../../components/ui/Button';
import Image from 'next/image';

export default function OcchialiContent() {
  const brands = [
    { id: '1', src: '/images/marchi/occhiali/rayban-logo.jpg', alt: 'Ray-Ban' },
    { id: '2', src: '/images/marchi/occhiali/persol-logo.jpg', alt: 'Persol' },
    { id: '3', src: '/images/marchi/occhiali/vogue-logo.jpg', alt: 'Vogue' },
    { id: '4', src: '/images/marchi/occhiali/Karl<PERSON>-logo.jpg', alt: '<PERSON>' },
    { id: '5', src: '/images/marchi/occhiali/Dsquared2-logo.jpg', alt: 'Dsquared2' },
    { id: '6', src: '/images/marchi/occhiali/<PERSON>-logo.jpg', alt: '<PERSON>' },
    { id: '7', src: '/images/marchi/occhiali/Swarowsky-logo.jpg', alt: 'Swarovski' },
    { id: '8', src: '/images/marchi/occhiali/Borbonese-logo.jpg', alt: 'Borbonese' },
    { id: '9', src: '/images/marchi/occhiali/Genny-logo.jpg', alt: 'Genny' },
    { id: '10', src: '/images/marchi/occhiali/Charmant-logo.jpg', alt: 'Charmant' },
    { id: '11', src: '/images/marchi/occhiali/Freeway-logo.jpg', alt: 'Freeway' },
    { id: '12', src: '/images/marchi/occhiali/RH-logo.jpg', alt: 'RH' },
  ];

  const lensManufacturers = [
    { name: 'Essilor', description: 'Leader mondiale nelle lenti oftalmiche' },
    { name: 'Hoya', description: 'Tecnologia giapponese di precisione' },
    { name: 'Rodenstock', description: 'Eccellenza tedesca dal 1877' },
    { name: 'Optovision', description: 'Innovazione italiana per la vista' },
    { name: 'SIA Ottica (Sel Oftalmica)', description: 'Qualità italiana certificata' },
    { name: 'RomanOptica', description: 'Tradizione romana di eccellenza' },
    { name: 'Zeiss', description: 'Ottica di precisione tedesca' },
  ];

  const occhialiImages = [
    {
      src: '/images/occhiali/DSC09413.jpeg',
      alt: 'Collezione occhiali da vista eleganti',
    },
    {
      src: '/images/occhiali/DSC09415.jpeg',
      alt: 'Montature moderne e di design',
    },
    {
      src: '/images/occhiali/DSC09451.jpeg',
      alt: 'Ampia selezione di occhiali da vista',
    },
  ];

  return (
    <div className="min-h-screen">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-b from-primary/5 to-background">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <p className="text-accent text-lg font-sans font-medium mb-4">
                  Soluzioni Personalizzate
                </p>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-sans text-text-base mb-6">
                  Occhiali da Vista
                </h1>
                <p className="text-xl text-text-base opacity-80 leading-relaxed mb-8">
                  Trova gli occhiali da vista perfetti per te con la nostra vasta collezione di montature di alta qualità e lenti tecnologicamente avanzate. I nostri esperti ti guideranno nella scelta ideale per il tuo stile e le tue esigenze visive.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="primary"
                    size="lg"
                    onClick={() => window.location.href = '/esami-vista'}
                  >
                    Prenota Controllo Vista
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={() => window.location.href = '/contatti'}
                  >
                    Scopri la Collezione
                  </Button>
                </div>
              </div>
              <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-xl">
                <Image
                  src="/images/occhiali/DSC09410.jpeg"
                  alt="Occhiali da vista di qualità"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Occhiali Gallery */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                La Nostra Collezione
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Scopri la nostra ampia selezione di montature per ogni stile e personalità
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {occhialiImages.map((image, index) => (
                <div key={index} className="relative h-64 rounded-lg overflow-hidden shadow-lg group">
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Lenti Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-8 text-center">
                Le Nostre Lenti da Vista: Innovazione e Precisione per una Visione Perfetta
              </h2>
              
              <div className="prose prose-lg max-w-none text-text-base opacity-90 leading-relaxed space-y-6">
                <p>
                  In Ottica GR1, la chiarezza visiva e il comfort sono la nostra priorità. Per questo, collaboriamo con i principali produttori di lenti da vista a livello mondiale, selezionando solo le tecnologie più avanzate e i materiali più performanti. Sappiamo che ogni occhio è unico e ogni esigenza visiva specifica; per questo, ti offriamo un'ampia scelta di lenti progettate per offrirti la massima qualità ottica, protezione e benessere per i tuoi occhi.
                </p>
                
                <p>
                  Dalle lenti monofocali e progressive di ultima generazione, alle soluzioni personalizzate per ogni stile di vita e ambiente (come lenti per guida, ufficio o sport), garantiamo una visione nitida e confortevole in ogni situazione. Il nostro team di esperti è pronto a guidarti nella scelta delle lenti perfette per te, ottimizzando ogni dettaglio in base alla tua prescrizione e alle tue abitudini.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Lens Manufacturers Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold font-sans text-text-base mb-6">
                I nostri marchi includono:
              </h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
              {lensManufacturers.map((manufacturer, index) => (
                <div key={index} className="bg-background p-6 rounded-lg text-center hover:shadow-lg transition-shadow duration-300">
                  <h4 className="text-xl font-semibold font-sans text-primary mb-3">
                    {manufacturer.name}
                  </h4>
                  <p className="text-text-base opacity-80 text-sm">
                    {manufacturer.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Brands Section */}
        <section className="py-16 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold font-sans text-text-base mb-4">
                I Nostri Brand
              </h2>
              <p className="text-lg text-text-base opacity-80 max-w-2xl mx-auto">
                Collaboriamo con i migliori marchi internazionali per offrirti montature di qualità superiore
              </p>
            </div>
            
            {brands.length > 0 && (
              <div className="max-w-6xl mx-auto">
                <Carousel
                  items={brands}
                  autoPlay={true}
                  autoPlayInterval={3500}
                  showDots={true}
                  showArrows={true}
                  itemsPerView={4}
                  className="brands-carousel carousel-container"
                />
              </div>
            )}
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-primary text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold font-sans mb-6">
              Prenota Controllo Vista
            </h2>
            <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
              Scopri la montatura e le lenti perfette per te. Prenota un controllo della vista 
              gratuito e lasciati guidare dai nostri esperti.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="accent"
                size="lg"
                onClick={() => window.location.href = '/esami-vista'}
              >
                Prenota Controllo Vista
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => window.location.href = '/contatti'}
                className="border-white text-white hover:bg-white hover:text-primary"
              >
                Contattaci
              </Button>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}
