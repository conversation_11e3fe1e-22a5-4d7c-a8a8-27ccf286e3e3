@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Colori Ottica GR1 */
  --primary: #7b0202;
  --primary-light: #a32222;
  --primary-dark: #4a0000;
  --background: #f9f6f4;
  --text-base: #2a2a2a;
  --accent: #b86f0e;
  --accent-light: #d88e2f;
  --border-gray: #dddddd;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background: var(--background);
  color: var(--text-base);
  font-family: var(--font-serif);
  line-height: 1.6;
  scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-sans);
  font-weight: 600;
  line-height: 1.2;
}

a {
  font-family: var(--font-sans);
  text-decoration: none;
  color: inherit;
}

/* Utility classes per effetti speciali */
.grayscale-hover {
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.grayscale-hover:hover {
  filter: grayscale(0%);
}

.backdrop-blur-custom {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .brands-carousel {
    --items-per-view: 2;
  }

  .hero-text {
    font-size: 2.5rem;
    line-height: 1.1;
  }

  .section-padding {
    padding: 2rem 0;
  }

  /* Enhanced header visibility on mobile */
  .header-mobile-bg {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }
}

/* Enhanced Carousel Animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.carousel-item-enter {
  animation: fadeInScale 0.6s ease-out;
}

.carousel-slide-enter-right {
  animation: slideInFromRight 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.carousel-slide-enter-left {
  animation: slideInFromLeft 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth GPU-accelerated transitions */
.carousel-container {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* Enhanced brand logo hover effects */
.brand-logo-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.brand-logo-hover:hover {
  transform: translateY(-2px) scale(1.05);
  filter: brightness(1.1) saturate(1.1);
}

@media (max-width: 480px) {
  .brands-carousel {
    --items-per-view: 1;
  }

  .hero-text {
    font-size: 2rem;
  }

  .container-mobile {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
